import requests
import json
from typing import List, Dict, Any
import os
from datetime import datetime
from dotenv import load_dotenv

class FileScanner:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.results = []
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json',
        }

    def list_directory(self, path: str) -> List[Dict[str, Any]]:
        """获取目录内容"""
        url = f"{self.base_url}/api/fs/list"
        response = requests.post(url, json={"Path": path}, headers=self.headers)
        if response.status_code == 200:
            data = response.json()
            if data["code"] == 200:
                return data["data"]["content"]
        return []

    def get_file_info(self, path: str) -> Dict[str, Any]:
        """获取文件详细信息"""
        url = f"{self.base_url}/api/fs/get"
        response = requests.post(url, json={"Path": path}, headers=self.headers)
        if response.status_code == 200:
            data = response.json()
            if data["code"] == 200:
                return data["data"]
        return {}

    def scan_directory(self, current_path: str = "/"):
        """递归扫描目录"""
        items = self.list_directory(current_path)
        
        for item in items:
            full_path = os.path.join(current_path, item["name"]).replace("\\", "/")
            
            if item["is_dir"]:
                print(f"扫描目录: {full_path}")
                self.scan_directory(full_path)
            else:
                print(f"获取文件信息: {full_path}")
                file_info = self.get_file_info(full_path)
                if file_info:
                    self.results.append(file_info)

    def save_results(self, filename: str = "scan_results.json"):
        """保存扫描结果"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)


def main():
    # 加载.env文件
    load_dotenv()
    
    # 从环境变量获取base_url
    base_url = 'https://www.asmrgay.com'    #os.getenv('BASE_URL')
    if not base_url:
        raise ValueError("请在.env文件中设置BASE_URL环境变量")
    
    scanner = FileScanner(base_url)

    # 单独获取目录数据
    _list_res = scanner.list_directory('/asmr/柚木つばめ')
    with open('list_柚木つばめ_results.json', 'w', encoding='utf-8') as f:
        json.dump(_list_res, f, ensure_ascii=False, indent=2)

    # 单独获取文件数据
    # _fd_res = scanner.get_file_info('/asmr/更新内容.md')
    # with open('asmr/list_更新内容_md_results.json', 'w', encoding='utf-8') as f:
    #     json.dump(_fd_res, f, ensure_ascii=False, indent=2)

    # https://asmr.121231234.xyz/asmr/%E6%9B%B4%E6%96%B0%E5%86%85%E5%AE%B9.md?sign=kymSol3kB4fkkju6X-v5eSm_apCrbBN5if-dzNMUpEw=:1827885316

    # print("开始扫描文件系统...")
    # scanner.scan_directory()
    #
    # print("保存扫描结果...")
    # scanner.save_results()
    # print("扫描完成！结果已保存到 scan_results.json")


if __name__ == "__main__":
    main()
