import sqlite3
import json
import os
import requests
from datetime import datetime
from typing import List, Dict, Any
from urllib.parse import quote
import time
import re

# 定义 REGEXP 函数
def regexp(expr, item):
    return re.match(expr, item) is not None

class DatabaseManager:
    def __init__(self, db_name="file_info.db", proxy=None):
        self.db_name = db_name
        self.conn = None
        self.cursor = None
        self.base_url = "https://www.asmrgay.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json',
        }
        self.proxies = {
            'http': proxy,
            'https': proxy
        } if proxy else None
        self.download_dir = os.path.expanduser('/mnt/nvme0n1-1/Public/Downloads/Priv/asmrgay')

    def connect(self):
        self.conn = sqlite3.connect(self.db_name)
        self.conn.create_function('REGEXP', 2, regexp)
        self.cursor = self.conn.cursor()
        self.create_tables()

    def create_tables(self):
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            size INTEGER,
            is_dir BOOLEAN,
            modified DATETIME,
            created DATETIME,
            sign TEXT,
            thumb TEXT,
            type INTEGER,
            hashinfo TEXT,
            hash_info TEXT,
            path TEXT UNIQUE,
            local_updated_at DATETIME,
            download_status TEXT DEFAULT 'pending',
            download_error TEXT
        )
        ''')
        self.conn.commit()

    def get_download_url(self, name: str, sign: str) -> str:
        encoded_name = '/'.join(quote(part, safe='') for part in name.split('/'))
        return f"https://asmr.121231234.xyz/{encoded_name}?sign={sign}"

    def list_directory(self, path: str) -> List[Dict[str, Any]]:
        """获取目录内容"""
        time.sleep(1)
        url = f"{self.base_url}/api/fs/list"
        response = requests.post(url, json={"Path": path}, headers=self.headers)
        if response.status_code == 200:

            data = response.json()

            with open('example.txt', 'a', encoding='utf-8') as file:
                file.write(str(data)+'\n')
            if data["code"] == 200:
                return data["data"]["content"]
            else:
                with open('error_code_not_200.txt', 'a', encoding='utf-8') as file:
                    file.write(str(response) + '\n')

        else:
            with open('error_not_200.txt', 'a', encoding='utf-8') as file:
                file.write(str(response) + '\n')
        return []

    def fetch_and_save_data(self, path="/"):
        try:
            data = self.list_directory(path)
            for item in data:
                current_path = os.path.join(path, item['name']).replace('\\', '/')
                existing_modified = self.get_existing_file(current_path)
                if not existing_modified or existing_modified != item['modified']:
                    # 只递归目录
                    if item['is_dir']:
                        self.fetch_and_save_data(current_path)
                    if existing_modified:
                        print(f"更新文件: {current_path}")
                        self.update_file(item, current_path)
                    else:
                        print(f"新增文件: {current_path}")
                        self.insert_file(item, current_path)
                else:
                    if item['is_dir']:
                        # 只在有未完成下载时递归
                        self.cursor.execute('''
                        SELECT COUNT(*) FROM files 
                        WHERE path LIKE ? || '/%' 
                        AND download_status != 'completed'
                        ''', (current_path,))
                        has_pending = self.cursor.fetchone()[0] > 0
                        if has_pending:
                            print(f"目录 {current_path} 下有未完成的下载，继续递归")
                            self.fetch_and_save_data(current_path)
                        else:
                            print(f"目录 {current_path} 未变化且无待下载，跳过递归")
            self.conn.commit()
            print(f"已处理路径: {path}")
        except Exception as e:
            print(f"处理错误: {e}")

    def get_existing_file(self, path):
        self.cursor.execute('SELECT modified FROM files WHERE path = ?', (path,))
        result = self.cursor.fetchone()
        return result[0] if result else None

    def update_file(self, item, path):
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.cursor.execute('''
        UPDATE files SET
            size = ?,
            is_dir = ?,
            modified = ?,
            created = ?,
            sign = ?,
            thumb = ?,
            type = ?,
            hashinfo = ?,
            hash_info = ?,
            local_updated_at = ?
        WHERE path = ?
        ''', (
            item['size'],
            item['is_dir'],
            item['modified'],
            item['created'],
            item.get('sign', ''),
            item.get('thumb', ''),
            item.get('type', 0),
            item.get('hashinfo', ''),
            item.get('hash_info', ''),
            current_time,
            path
        ))

    def insert_file(self, item, path):
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.cursor.execute('''
        INSERT INTO files (
            name, size, is_dir, modified, created, 
            sign, thumb, type, hashinfo, hash_info, path,
            local_updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            item['name'],
            item['size'],
            item['is_dir'],
            item['modified'],
            item['created'],
            item.get('sign', ''),
            item.get('thumb', ''),
            item.get('type', 0),
            item.get('hashinfo', ''),
            item.get('hash_info', ''),
            path,
            current_time
        ))

    def download_file(self, name: str, path:str, sign: str, save_path: str, total_size: int = None) -> bool:
        url = self.get_download_url(path, sign)
        try:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            if os.path.exists(save_path):
                os.remove(save_path)
            headers = self.headers.copy()
            with requests.get(url, headers=headers, proxies=self.proxies, stream=True, timeout=60) as r:
                r.raise_for_status()
                if total_size is None:
                    total_size = int(r.headers.get('Content-Length', 0))
                    if not total_size:
                        total_size = None
                downloaded = 0
                chunk_size = 81920
                with open(save_path, 'wb') as f:
                    for chunk in r.iter_content(chunk_size=chunk_size):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            if total_size:
                                percent = downloaded / total_size * 100
                                print(f"\r下载进度: {percent:.2f}% ({downloaded}/{total_size} bytes)", end='')
                print(f"\n下载完成: {save_path}")
            self.cursor.execute('''
                UPDATE files SET download_status = 'completed', download_error = NULL WHERE name = ? AND sign = ?
            ''', (name, sign))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"下载失败 {name}: {str(e)}")
            self.cursor.execute('''
                UPDATE files SET download_status = 'error', download_error = ? WHERE name = ? AND sign = ?
            ''', (str(e), name, sign))
            self.conn.commit()
            return False

    def download_pending_files(self):
        self.cursor.execute('''
        SELECT name, sign, path, size FROM files 
        WHERE download_status != 'completed' 
        AND is_dir = 0 
        AND sign != '' AND path REGEXP '^(/asmr/中文音声|/asmr6|/asmr/清软喵|/asmr/音声汉化)'
        ''')
        for name, sign, path, size in self.cursor.fetchall():
            # "/asmr/橙澄子汉化组",
            # "/asmr/风花雪月汉化组",
            # AND path IN ( "/asmr/中文音声",
            #               "/asmr/清软喵",
            #               "/asmr/音声汉化",
            #               "/asmr6"
            self.cursor.execute('''
                SELECT download_status FROM files WHERE name = ? AND sign = ?
                AND path REGEXP '^(/asmr/中文音声|/asmr6|/asmr/清软喵|/asmr/音声汉化)'
            ''', (name, sign))
            result = self.cursor.fetchone()
            if result and result[0] == 'completed':
                print(f"文件已下载完成，跳过: {path}")
                continue
            save_path = os.path.join(self.download_dir, path.lstrip('/'))
            if self.download_file(name, path, sign, save_path, total_size=size):
                print(f"已下载: {path}")
            else:
                print(f"下载失败: {path}")

    def close(self):
        if self.conn:
            self.conn.close()

def main():
    db_manager = DatabaseManager(proxy=None)
    db_manager.connect()
    db_manager.fetch_and_save_data()
    db_manager.download_pending_files()
    db_manager.close()
    print("同步与下载全部完成！")

if __name__ == "__main__":
    main() 