from urllib.parse import quote
import m3u8
import requests
import os
import ffmpeg
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import urljoin

def download_segment(url, output_path, headers=None):
    """下载单个分片"""
    try:
        response = requests.get(url, headers=headers, stream=True)
        if response.status_code == 200:
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            return True
        print(f"下载分片 {url} 失败: 状态码 {response.status_code}")
        return False
    except Exception as e:
        print(f"下载分片 {url} 失败: {e}")
        return False

def download_m3u8_segments(m3u8_url, output_dir, headers=None, max_workers=10):
    """下载M3U8所有分片"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    m3u8_obj = m3u8.load(m3u8_url, headers=headers)
    segments = m3u8_obj.segments
    if not segments:
        raise ValueError("M3U8文件不包含任何分片")
    print(f"总分片数: {len(segments)}")

    segment_files = []

    for i, segment in enumerate(segments):
        segment_url = urljoin(m3u8_url, segment.uri)
        segment_file = os.path.join(output_dir, f"segment_{i:04d}.ts")
        segment_files.append(segment_file)
    # with ThreadPoolExecutor(max_workers=max_workers) as executor:
    #     futures = []
    #     for i, segment in enumerate(segments):
    #         segment_url = urljoin(m3u8_url, segment.uri)
    #         segment_file = os.path.join(output_dir, f"segment_{i:04d}.ts")
    #         segment_files.append(segment_file)
    #         futures.append(executor.submit(download_segment, segment_url, segment_file, headers))
    #
    #     for i, future in enumerate(futures):
    #         if not future.result():
    #             print(f"分片 {i} 下载失败")

    for segment_file in segment_files:
        if not os.path.exists(segment_file):
            raise FileNotFoundError(f"分片缺失: {segment_file}")
        if os.path.getsize(segment_file) == 0:
            raise ValueError(f"分片为空: {segment_file}")

    return segment_files, m3u8_obj

def merge_ts_to_mp4(segment_files, output_mp4, delete_ts=False, m3u8_obj=None, headers=None):
    """合并TS文件并转换为MP4（与手动命令一致）"""
    try:
        # 规范化输出路径
        output_mp4 = os.path.abspath(output_mp4)
        if not output_mp4:
            raise ValueError("output_mp4 路径为空")
        print(f"输出路径: {output_mp4}")

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_mp4) or '.', exist_ok=True)

        # 检查分片文件
        if not segment_files:
            raise ValueError("分片文件列表为空")
        for ts_file in segment_files:
            if not os.path.exists(ts_file):
                raise FileNotFoundError(f"分片文件缺失: {ts_file}")
            if os.path.getsize(ts_file) == 0:
                raise ValueError(f"分片文件为空: {ts_file}")

        # 创建合并列表文件
        concat_file = "concat_list.txt"
        with open(concat_file, 'w', encoding='utf-8') as f:
            for ts_file in segment_files:
                f.write(f"file '{os.path.abspath(ts_file)}'\n")
        print(f"合并列表已生成: {concat_file}")

        # 使用 FFmpeg 合并，精确匹配手动命令
        try:
            stream = ffmpeg.input(concat_file, format='concat', safe=0)
            stream = stream.output(output_mp4, c='copy')
            ffmpeg.run(stream, overwrite_output=True, capture_stdout=True, capture_stderr=True)
        except ffmpeg.Error as e:
            with open("ffmpeg_error.log", "w") as f:
                f.write(e.stderr.decode())
            print(f"FFmpeg 错误: 已保存到 ffmpeg_error.log")
            raise

        # 验证输出文件
        if not os.path.exists(output_mp4):
            raise FileNotFoundError(f"未生成输出文件: {output_mp4}")
        print(f"已成功生成MP4文件: {output_mp4}")

        # 清理临时文件
        # os.remove(concat_file)
        # if delete_ts:
        #     for ts_file in segment_files:
        #         os.remove(ts_file)
        #     os.rmdir(os.path.dirname(segment_files[0]))

    except Exception as e:
        print(f"合并失败: {e}")
        raise

def download_m3u8_to_mp4(m3u8_url, output_mp4, headers=None, temp_dir="temp_segments"):
    """主函数：下载M3U8并转换为MP4"""
    try:
        segment_files, m3u8_obj = download_m3u8_segments(m3u8_url, temp_dir, headers)
        merge_ts_to_mp4(segment_files, output_mp4, delete_ts=True, m3u8_obj=m3u8_obj, headers=headers)
    except Exception as e:
        print(f"处理失败: {e}")


def get_download_url(name: str, sign: str) -> str:
    encoded_name = '/'.join(quote(part, safe='') for part in name.split('/'))
    return f"https://asmr.121231234.xyz/{encoded_name}?sign={sign}"


if __name__ == "__main__":
    # https://asmr.121231234.xyz//asmr6/%E9%9B%AA%E5%84%BF%E5%9C%86%E5%9C%86/%E5%B0%8F%E5%AD%A6%E4%B8%80%E5%B9%B4%E7%BA%A7%E4%B8%8A%E6%9C%9F%E8%B5%84%E6%96%99.m3u8?sign=b11jOAnGc9D_fZvaQc7lQ1P1u4DxePI1NhZWkA-s5HA=:1828698970
    # ffmpeg -f concat -safe 0 -i concat_list.txt -c copy -bsf:v h264_mp4toannexb output.mp4
    # ffmpeg -f concat -safe 0 -i concat_list.txt -c copy output.mp4
    _path = "/asmr6/雪儿圆圆/小学一年级上期资料.m3u8"
    m3u8_url = get_download_url(_path, "b11jOAnGc9D_fZvaQc7lQ1P1u4DxePI1NhZWkA-s5HA=:1828698970")
    print(m3u8_url)
    # output_mp4 = "/root/db/m3u8/output.mp4"  # 使用绝对路径
    output_mp4 = "output.mp4"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }

    download_m3u8_to_mp4(m3u8_url, output_mp4, headers)